const express = require('express');
const crypto = require('crypto');
const oauthHandler = require('../oauth');
const { jwtAuthMiddleware } = require('../middleware/auth');

const router = express.Router();

// OAuth状态存储
let globalOAuthState = null;

// 创建OAuth状态
function createOAuthState() {
    const codeVerifierBytes = crypto.randomBytes(32);
    const codeVerifier = codeVerifierBytes.toString('base64url');
    const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
    const state = crypto.randomBytes(8).toString('base64url');
    
    return {
        codeVerifier,
        codeChallenge,
        state,
        creationTime: new Date()
    };
}

// 生成授权URL
function generateAuthorizeURL(oauthState) {
    const params = new URLSearchParams({
        response_type: 'code',
        code_challenge: oauthState.codeChallenge,
        client_id: 'v',
        state: oauthState.state,
        prompt: 'login'
    });
    
    return `https://auth.augmentcode.com/authorize?${params.toString()}`;
}

// OAuth授权端点（需要登录）
router.get('/auth', jwtAuthMiddleware, (req, res) => {
    globalOAuthState = createOAuthState();
    const authorizeURL = generateAuthorizeURL(globalOAuthState);
    res.json({ authorize_url: authorizeURL });
});

// OAuth回调端点（需要登录）
router.post('/callback', jwtAuthMiddleware, (req, res) => {
    // 将全局状态传递给处理器
    global.oauthState = globalOAuthState;
    oauthHandler.handleCallback(req, res);
});

module.exports = router;
