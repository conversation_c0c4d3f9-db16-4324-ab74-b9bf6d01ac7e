const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const logger = require('./logger');
const config = require('./config');
const oauth = require('./oauth');

// 认证中间件
function authMiddleware(req, res, next) {
    // 如果未设置 AuthToken，则不启用鉴权
    const authToken = config.get('AUTH_TOKEN');
    if (!authToken) {
        return next();
    }
    
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        logger.error('Authorization header is empty');
        return res.status(401).json({ error: 'Authorization header is required' });
    }
    
    // 支持 "Bearer <token>" 格式
    const token = authHeader.replace(/^Bearer\s+/, '').trim();
    
    if (token !== authToken) {
        logger.error(`Invalid authorization token: ${token}`);
        return res.status(401).json({ error: 'Invalid authorization token' });
    }
    
    next();
}

// 获取模型列表
function getModels(req, res) {
    const models = [
        {
            id: 'claude-3.5-sonnet-chat',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'augment'
        },
        {
            id: 'claude-3.5-sonnet-agent',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'augment'
        },
        {
            id: 'gpt-4o',
            object: 'model',
            created: Math.floor(Date.now() / 1000),
            owned_by: 'augment'
        }
    ];
    
    res.json({
        object: 'list',
        data: models
    });
}

// 检测语言
function detectLanguage(messages) {
    if (!messages || messages.length === 0) {
        return '';
    }
    
    const content = messages[messages.length - 1].content || '';
    
    // 简单的语言检测逻辑
    if (/[\u4e00-\u9fff]/.test(content)) {
        return 'Chinese';
    } else if (/\b(function|class|const|let|var|if|else|for|while)\b/.test(content)) {
        return 'JavaScript';
    } else if (/\b(def|class|import|from|if|else|for|while)\b/.test(content)) {
        return 'Python';
    } else if (/\b(public|private|class|interface|extends|implements)\b/.test(content)) {
        return 'Java';
    }
    
    return 'HTML';
}

// 生成CheckpointID
function generateCheckpointID() {
    const timestamp = Date.now().toString();
    return crypto.createHash('sha256').update(timestamp).digest('hex');
}

// 生成请求ID
function generateRequestID() {
    return uuidv4();
}

// 转换为Augment请求格式
function convertToAugmentRequest(openaiRequest) {
    const { model, messages } = openaiRequest;
    
    // 确定模式和其他参数基于模型名称
    let mode = 'CHAT'; // 默认使用CHAT模式
    let userGuideLines = 'must answer in Chinese.';
    let includeToolDefinitions = false;
    let includeDefaultPrompt = false;
    
    // 将模型名称转换为小写，然后检查后缀
    const modelLower = model.toLowerCase();
    
    if (modelLower.endsWith('-chat')) {
        mode = 'CHAT';
    } else if (modelLower.endsWith('-agent')) {
        mode = 'AGENT';
        userGuideLines = 'must answer in Chinese, do not use tools, and for questions involving internet searches, please answer based on your existing knowledge.';
        includeToolDefinitions = true;
        includeDefaultPrompt = true;
    }
    
    const augmentReq = {
        path: '',
        mode: mode,
        prefix: 'You are AI assistant,help me to solve problems!',
        suffix: ' ',
        lang: detectLanguage(messages),
        message: '',
        user_guidelines: userGuideLines,
        chat_history: [],
        blobs: {
            checkpoint_id: generateCheckpointID(),
            added_blobs: [],
            deleted_blobs: []
        },
        user_guided_blobs: [],
        external_source_ids: [],
        feature_detection_flags: {
            support_raw_output: true
        },
        tool_definitions: [],
        nodes: []
    };
    
    // 处理消息历史
    if (messages.length > 1) {
        for (let i = 0; i < messages.length - 1; i += 2) {
            if (i + 1 < messages.length) {
                const userMsg = messages[i];
                const assistantMsg = messages[i + 1];
                
                const chatHistory = {
                    response_text: assistantMsg.content,
                    request_message: userMsg.content,
                    request_id: generateRequestID(),
                    request_nodes: [],
                    response_nodes: [{
                        id: 0,
                        type: 0,
                        content: assistantMsg.content,
                        tool_use: {
                            tool_use_id: '',
                            tool_name: '',
                            input_json: ''
                        },
                        agent_memory: {
                            content: ''
                        }
                    }]
                };
                augmentReq.chat_history.push(chatHistory);
            }
        }
    }
    
    // 设置当前消息
    if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];
        const defaultPrompt = 'Your are claude4, All replies cannot create, modify, or delete files, and must provide content directly!';
        
        if (includeDefaultPrompt) {
            augmentReq.message = defaultPrompt + '\n' + lastMsg.content;
        } else {
            augmentReq.message = lastMsg.content;
        }
    }
    
    return augmentReq;
}

// 创建HTTP客户端
function createHttpClient() {
    const proxyURL = config.get('PROXY_URL');
    const axiosConfig = {
        timeout: 60000,
        headers: {
            'User-Agent': config.get('USER_AGENT')
        }
    };

    if (proxyURL) {
        try {
            const proxyUrl = new URL(proxyURL);
            axiosConfig.proxy = {
                protocol: proxyUrl.protocol.slice(0, -1),
                host: proxyUrl.hostname,
                port: parseInt(proxyUrl.port) || 8080
            };
            logger.info(`使用代理: ${proxyURL}`);
        } catch (error) {
            logger.error('代理URL格式错误:', error.message);
        }
    }

    return axios.create(axiosConfig);
}

// 估算token数量
function estimateTokenCount(text) {
    if (!text) return 0;

    // 按空格分割英文单词
    const words = text.split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;

    // 计算中文字符数量
    const chineseCount = (text.match(/[\u4e00-\u9fff]/g) || []).length;

    // 粗略估计：英文单词按1个token计算，中文字符按0.75个token计算
    return wordCount + Math.floor(chineseCount * 0.75);
}

// 处理聊天完成请求
async function chatCompletions(req, res) {
    try {
        const { model, messages, stream = false } = req.body;

        if (!messages || !Array.isArray(messages) || messages.length === 0) {
            return res.status(400).json({ error: '无效的请求数据' });
        }

        // 获取token和tenant_url
        const { token, tenantURL } = oauth.getAuthInfo();

        if (!token || !tenantURL) {
            return res.status(401).json({ error: '无可用Token,请先通过OAuth获取' });
        }

        // 转换为Augment请求格式
        const augmentReq = convertToAugmentRequest(req.body);

        if (stream) {
            // 延迟加载stream模块以避免循环依赖
            const { handleStreamRequest } = require('./stream');
            await handleStreamRequest(req, res, augmentReq, model, token, tenantURL);
        } else {
            // 延迟加载stream模块以避免循环依赖
            const { handleNonStreamRequest } = require('./stream');
            await handleNonStreamRequest(req, res, augmentReq, model, token, tenantURL);
        }

    } catch (error) {
        logger.error('处理聊天请求失败:', error.message);
        res.status(500).json({ error: '服务器内部错误' });
    }
}

module.exports = {
    authMiddleware,
    getModels,
    chatCompletions,
    convertToAugmentRequest,
    detectLanguage,
    generateCheckpointID,
    generateRequestID,
    createHttpClient,
    estimateTokenCount
};
