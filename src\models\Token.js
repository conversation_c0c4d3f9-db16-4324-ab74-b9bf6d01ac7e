const database = require('../database');
const logger = require('../logger');

class Token {
    constructor(data = {}) {
        this.id = data.id || null;
        this.token = data.token || null;
        this.tenant_url = data.tenant_url || null;
        this.creator_email = data.creator_email || null;
        this.user_ck = data.user_ck || null;
        this.use_time = data.use_time || null;
        this.destroy_time = data.destroy_time || null;
        this.created_at = data.created_at || null;
        this.updated_at = data.updated_at || null;
    }
    
    // 创建Token
    static async create(tokenData) {
        try {
            const { token, tenant_url, creator_email, user_ck } = tokenData;
            
            const sql = `INSERT INTO tokens (token, tenant_url, creator_email, user_ck) 
                        VALUES (?, ?, ?, ?)`;
            const { rows } = await database.query(sql, [token, tenant_url, creator_email, user_ck]);
            
            logger.info(`Token创建成功: 创建者=${creator_email}`);
            
            // 返回创建的Token
            return await Token.findById(rows.insertId);
        } catch (error) {
            logger.error('创建Token失败:', error.message);
            throw error;
        }
    }
    
    // 根据ID查找Token
    static async findById(id) {
        try {
            const sql = 'SELECT * FROM tokens WHERE id = ?';
            const { rows } = await database.query(sql, [id]);
            
            if (rows.length === 0) {
                return null;
            }
            
            return new Token(rows[0]);
        } catch (error) {
            logger.error('查找Token失败:', error.message);
            throw error;
        }
    }
    
    // 根据创建者邮箱查找Token
    static async findByCreatorEmail(email) {
        try {
            const sql = 'SELECT * FROM tokens WHERE creator_email = ? ORDER BY created_at DESC';
            const { rows } = await database.query(sql, [email]);
            
            return rows.map(row => new Token(row));
        } catch (error) {
            logger.error('查找Token失败:', error.message);
            throw error;
        }
    }
    
    // 根据用户CK查找Token
    static async findByUserCk(user_ck) {
        try {
            const sql = 'SELECT * FROM tokens WHERE user_ck = ? ORDER BY created_at DESC';
            const { rows } = await database.query(sql, [user_ck]);
            
            return rows.map(row => new Token(row));
        } catch (error) {
            logger.error('查找Token失败:', error.message);
            throw error;
        }
    }
    
    // 获取可用的Token（未销毁的）
    static async findAvailable() {
        try {
            const sql = 'SELECT * FROM tokens WHERE destroy_time IS NULL ORDER BY created_at DESC';
            const { rows } = await database.query(sql);
            
            return rows.map(row => new Token(row));
        } catch (error) {
            logger.error('获取可用Token失败:', error.message);
            throw error;
        }
    }
    
    // 获取所有Token
    static async findAll() {
        try {
            const sql = 'SELECT * FROM tokens ORDER BY created_at DESC';
            const { rows } = await database.query(sql);
            
            return rows.map(row => new Token(row));
        } catch (error) {
            logger.error('获取Token列表失败:', error.message);
            throw error;
        }
    }
    
    // 更新使用时间
    async updateUseTime() {
        try {
            const sql = 'UPDATE tokens SET use_time = CURRENT_TIMESTAMP WHERE id = ?';
            await database.query(sql, [this.id]);
            
            this.use_time = new Date();
            logger.info(`Token使用时间更新: ID=${this.id}`);
            return true;
        } catch (error) {
            logger.error('更新Token使用时间失败:', error.message);
            throw error;
        }
    }
    
    // 销毁Token
    async destroy() {
        try {
            const sql = 'UPDATE tokens SET destroy_time = CURRENT_TIMESTAMP WHERE id = ?';
            await database.query(sql, [this.id]);

            this.destroy_time = new Date();
            logger.info(`Token销毁成功: ID=${this.id}`);
            return true;
        } catch (error) {
            logger.error('销毁Token失败:', error.message);
            throw error;
        }
    }

    // 查询user_ck为空的可用Token（支持分页）
    static async findAvailableWithoutUserCk(options = {}) {
        try {
            const { page = 1, limit = 10, creatorEmail = null } = options;
            const offset = (page - 1) * limit;

            let sql = `SELECT * FROM tokens
                      WHERE user_ck IS NULL AND destroy_time IS NULL`;
            let countSql = `SELECT COUNT(*) as total FROM tokens
                           WHERE user_ck IS NULL AND destroy_time IS NULL`;
            let params = [];
            let countParams = [];

            // 如果指定了创建者邮箱，只查询该用户创建的token
            if (creatorEmail) {
                sql += ' AND creator_email = ?';
                countSql += ' AND creator_email = ?';
                params.push(creatorEmail);
                countParams.push(creatorEmail);
            }

            sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            // 查询数据和总数
            const [dataResult, countResult] = await Promise.all([
                database.query(sql, params),
                database.query(countSql, countParams)
            ]);

            const tokens = dataResult.rows.map(row => new Token(row));
            const total = countResult.rows[0].total;

            return {
                tokens,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            logger.error('查询可用Token失败:', error.message);
            throw error;
        }
    }

    // 根据token值更新user_ck
    static async updateUserCkByToken(tokenValue, userCk, creatorEmail = null) {
        try {
            let sql = 'UPDATE tokens SET user_ck = ?, updated_at = CURRENT_TIMESTAMP WHERE token = ? AND destroy_time IS NULL';
            let params = [userCk, tokenValue];

            // 如果提供了创建者邮箱，则只允许修改自己创建的token
            if (creatorEmail) {
                sql += ' AND creator_email = ?';
                params.push(creatorEmail);
            }

            const { rows } = await database.query(sql, params);

            if (rows.affectedRows === 0) {
                throw new Error('Token不存在、已销毁或无权限修改');
            }

            logger.info(`Token user_ck更新成功: token=${tokenValue.substring(0, 10)}..., user_ck=${userCk}`);
            return true;
        } catch (error) {
            logger.error('更新Token user_ck失败:', error.message);
            throw error;
        }
    }

    // 根据token值更新user_ck
    static async updateUserCkByToken(tokenValue, userCk, creatorEmail = null) {
        try {
            let sql = 'UPDATE tokens SET user_ck = ?, updated_at = CURRENT_TIMESTAMP WHERE token = ?';
            let params = [userCk, tokenValue];

            // 如果提供了创建者邮箱，则只允许修改自己创建的token
            if (creatorEmail) {
                sql += ' AND creator_email = ?';
                params.push(creatorEmail);
            }

            const { rows } = await database.query(sql, params);

            if (rows.affectedRows === 0) {
                throw new Error('Token不存在或无权限修改');
            }

            logger.info(`Token user_ck更新成功: token=${tokenValue.substring(0, 10)}..., user_ck=${userCk}`);
            return true;
        } catch (error) {
            logger.error('更新Token user_ck失败:', error.message);
            throw error;
        }
    }

    // 根据token值查找Token
    static async findByToken(tokenValue) {
        try {
            const sql = 'SELECT * FROM tokens WHERE token = ?';
            const { rows } = await database.query(sql, [tokenValue]);

            if (rows.length === 0) {
                return null;
            }

            return new Token(rows[0]);
        } catch (error) {
            logger.error('根据token查找Token失败:', error.message);
            throw error;
        }
    }
    
    // 检查Token是否可用
    isAvailable() {
        return this.destroy_time === null;
    }
    
    // 转换为JSON
    toJSON() {
        return {
            id: this.id,
            token: this.token,
            tenant_url: this.tenant_url,
            creator_email: this.creator_email,
            user_ck: this.user_ck,
            use_time: this.use_time,
            destroy_time: this.destroy_time,
            created_at: this.created_at,
            updated_at: this.updated_at,
            is_available: this.isAvailable()
        };
    }
}

module.exports = Token;
