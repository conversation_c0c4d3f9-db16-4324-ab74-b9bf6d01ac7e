const database = require('../database');
const { v4: uuidv4 } = require('uuid');
const logger = require('../logger');

class User {
    constructor(data = {}) {
        this.uuid = data.uuid || null;
        this.email = data.email || null;
        this.auth_token = data.auth_token || null;
        this.token_expires_at = data.token_expires_at || null;
        this.created_at = data.created_at || null;
        this.updated_at = data.updated_at || null;
    }
    
    // 创建用户
    static async create(email) {
        try {
            const uuid = uuidv4();
            const sql = 'INSERT INTO users (uuid, email) VALUES (?, ?)';
            await database.query(sql, [uuid, email]);
            
            logger.info(`用户创建成功: ${email}`);
            return new User({ uuid, email });
        } catch (error) {
            if (error.code === 'ER_DUP_ENTRY') {
                throw new Error('邮箱已存在');
            }
            logger.error('创建用户失败:', error.message);
            throw error;
        }
    }
    
    // 根据邮箱查找用户
    static async findByEmail(email) {
        try {
            const sql = 'SELECT * FROM users WHERE email = ?';
            const { rows } = await database.query(sql, [email]);
            
            if (rows.length === 0) {
                return null;
            }
            
            return new User(rows[0]);
        } catch (error) {
            logger.error('查找用户失败:', error.message);
            throw error;
        }
    }
    
    // 根据UUID查找用户
    static async findByUuid(uuid) {
        try {
            const sql = 'SELECT * FROM users WHERE uuid = ?';
            const { rows } = await database.query(sql, [uuid]);
            
            if (rows.length === 0) {
                return null;
            }
            
            return new User(rows[0]);
        } catch (error) {
            logger.error('查找用户失败:', error.message);
            throw error;
        }
    }
    
    // 获取或创建用户
    static async findOrCreate(email) {
        try {
            let user = await User.findByEmail(email);
            if (!user) {
                user = await User.create(email);
            }
            return user;
        } catch (error) {
            logger.error('获取或创建用户失败:', error.message);
            throw error;
        }
    }
    
    // 获取所有用户
    static async findAll() {
        try {
            const sql = 'SELECT * FROM users ORDER BY created_at DESC';
            const { rows } = await database.query(sql);
            
            return rows.map(row => new User(row));
        } catch (error) {
            logger.error('获取用户列表失败:', error.message);
            throw error;
        }
    }
    
    // 删除用户
    async delete() {
        try {
            const sql = 'DELETE FROM users WHERE uuid = ?';
            await database.query(sql, [this.uuid]);
            
            logger.info(`用户删除成功: ${this.email}`);
            return true;
        } catch (error) {
            logger.error('删除用户失败:', error.message);
            throw error;
        }
    }
    
    // 转换为JSON
    toJSON() {
        return {
            uuid: this.uuid,
            email: this.email,
            auth_token: this.auth_token,
            token_expires_at: this.token_expires_at,
            created_at: this.created_at,
            updated_at: this.updated_at
        };
    }
}

module.exports = User;
