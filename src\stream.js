const logger = require('./logger');
const { createHttpClient, estimateTokenCount } = require('./api');

const errBlocked = 'Request blocked. Please reach <NAME_EMAIL> if you think this was a mistake.';

// 处理流式请求
async function handleStreamRequest(req, res, augmentReq, model, token, tenantURL) {
    try {
        // 设置SSE响应头
        res.writeHead(200, {
            'Content-Type': 'text/plain; charset=utf-8',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': '*'
        });
        
        const client = createHttpClient();
        const requestURL = tenantURL + 'chat-stream';
        
        // 提取主机部分
        const url = new URL(tenantURL);
        const hostName = url.host;
        
        const response = await client.post(requestURL, augmentReq, {
            headers: {
                'Host': hostName,
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            responseType: 'stream'
        });
        
        const responseID = `chatcmpl-${Date.now()}`;
        let fullText = '';
        let hasError = false;
        
        response.data.on('data', (chunk) => {
            const lines = chunk.toString().split('\n');
            
            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;
                
                try {
                    const augmentResp = JSON.parse(trimmedLine);
                    
                    // 检查响应内容是否包含错误信息
                    if (augmentResp.text && augmentResp.text.includes(errBlocked)) {
                        hasError = true;
                        logger.withFields({ token, mode: augmentReq.mode }).info('检测到block信息');
                        break;
                    }
                    
                    fullText += augmentResp.text || '';
                    
                    // 创建OpenAI兼容的流式响应
                    const streamResp = {
                        id: responseID,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: model,
                        choices: [{
                            index: 0,
                            delta: {
                                role: 'assistant',
                                content: augmentResp.text || ''
                            },
                            finish_reason: null
                        }]
                    };
                    
                    // 如果是最后一条消息，设置完成原因
                    if (augmentResp.done) {
                        streamResp.choices[0].finish_reason = 'stop';
                    }
                    
                    // 发送响应
                    res.write(`data: ${JSON.stringify(streamResp)}\n\n`);
                    
                    // 如果完成，发送最后的[DONE]标记
                    if (augmentResp.done) {
                        res.write('data: [DONE]\n\n');
                        res.end();
                        return;
                    }
                    
                } catch (parseError) {
                    logger.error('解析响应失败:', parseError.message);
                    continue;
                }
            }
        });
        
        response.data.on('end', () => {
            if (!res.headersSent) {
                res.write('data: [DONE]\n\n');
            }
            res.end();
        });
        
        response.data.on('error', (error) => {
            logger.error('流式响应错误:', error.message);
            if (!res.headersSent) {
                res.status(500).json({ error: '流式响应错误' });
            } else {
                res.end();
            }
        });
        
    } catch (error) {
        logger.error('处理流式请求失败:', error.message);
        
        // 如果是AGENT模式失败，尝试切换到CHAT模式
        if (augmentReq.mode === 'AGENT') {
            logger.info('尝试切换到CHAT模式重新请求');
            augmentReq.mode = 'CHAT';
            augmentReq.user_guidelines = '使用中文回答';
            augmentReq.tool_definitions = [];
            
            try {
                await handleStreamRequest(req, res, augmentReq, model, token, tenantURL);
                return;
            } catch (retryError) {
                logger.error('CHAT模式重试也失败:', retryError.message);
            }
        }
        
        if (!res.headersSent) {
            res.status(500).json({ error: error.message });
        }
    }
}

// 处理非流式请求
async function handleNonStreamRequest(req, res, augmentReq, model, token, tenantURL) {
    try {
        const client = createHttpClient();
        const requestURL = tenantURL + 'chat-stream';
        
        // 提取主机部分
        const url = new URL(tenantURL);
        const hostName = url.host;
        
        const response = await client.post(requestURL, augmentReq, {
            headers: {
                'Host': hostName,
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            responseType: 'stream'
        });
        
        let fullText = '';
        
        return new Promise((resolve, reject) => {
            response.data.on('data', (chunk) => {
                const lines = chunk.toString().split('\n');
                
                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (!trimmedLine) continue;
                    
                    try {
                        const augmentResp = JSON.parse(trimmedLine);
                        
                        // 检查响应内容是否包含错误信息
                        if (augmentResp.text && augmentResp.text.includes(errBlocked)) {
                            logger.withFields({ token, mode: augmentReq.mode }).info('检测到block信息');
                        }
                        
                        fullText += augmentResp.text || '';
                        
                        if (augmentResp.done) {
                            // 构建OpenAI兼容的响应
                            const promptTokens = estimateTokenCount(augmentReq.message);
                            const completionTokens = estimateTokenCount(fullText);
                            
                            const openAIResp = {
                                id: `chatcmpl-${Date.now()}`,
                                object: 'chat.completion',
                                created: Math.floor(Date.now() / 1000),
                                model: model,
                                choices: [{
                                    index: 0,
                                    message: {
                                        role: 'assistant',
                                        content: fullText
                                    },
                                    finish_reason: 'stop'
                                }],
                                usage: {
                                    prompt_tokens: promptTokens,
                                    completion_tokens: completionTokens,
                                    total_tokens: promptTokens + completionTokens
                                }
                            };
                            
                            res.json(openAIResp);
                            resolve();
                            return;
                        }
                        
                    } catch (parseError) {
                        logger.error('解析响应失败:', parseError.message);
                        continue;
                    }
                }
            });
            
            response.data.on('error', (error) => {
                logger.error('非流式响应错误:', error.message);
                reject(error);
            });
        });
        
    } catch (error) {
        logger.error('处理非流式请求失败:', error.message);
        throw error;
    }
}

module.exports = {
    handleStreamRequest,
    handleNonStreamRequest
};
