const axios = require('axios');
const logger = require('./logger');
const config = require('./config');

// 获取访问令牌
async function getAccessToken(tenantURL, codeVerifier, code) {
    try {
        const data = {
            grant_type: 'authorization_code',
            client_id: 'v',
            code_verifier: codeVerifier,
            redirect_uri: '',
            code: code
        };
        
        const response = await axios.post(tenantURL + 'token', data, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data && response.data.access_token) {
            return response.data.access_token;
        } else {
            throw new Error('响应中没有访问令牌');
        }
    } catch (error) {
        if (error.response) {
            throw new Error(`请求令牌失败: ${error.response.status} ${error.response.statusText}`);
        } else {
            throw new Error(`请求令牌失败: ${error.message}`);
        }
    }
}

// 设置认证信息
function setAuthInfo(token, tenant) {
    global.accessToken = token;
    global.tenantURL = tenant;
    logger.info('认证信息已更新');
}

// 获取认证信息
function getAuthInfo() {
    if (config.get('CODING_MODE') === 'true') {
        // 调试模式
        return {
            token: config.get('CODING_TOKEN'),
            tenantURL: config.get('TENANT_URL')
        };
    }

    // 返回内存中的token和tenantURL
    return {
        token: global.accessToken,
        tenantURL: global.tenantURL
    };
}

// 处理OAuth回调
async function handleCallback(req, res) {
    try {
        const { code, state, tenant_url } = req.body;

        if (!code || !state || !tenant_url) {
            return res.status(400).json({ error: '无效的请求数据' });
        }

        // 验证state（可选，增强安全性）
        if (global.oauthState && global.oauthState.state !== state) {
            return res.status(400).json({ error: '无效的state参数' });
        }

        // 使用授权码获取访问令牌
        const token = await getAccessToken(
            tenant_url,
            global.oauthState ? global.oauthState.codeVerifier : '',
            code
        );

        // 保存token到数据库（而不是内存）
        const Token = require('./models/Token');
        const savedToken = await Token.create({
            token: token,
            tenant_url: tenant_url,
            creator_email: req.user.email, // 用户邮箱（已通过认证中间件验证）
            user_ck: null // 首页创建的token没有user_ck
        });

        logger.info(`Token保存到数据库成功: ${req.user.email}, token_id: ${savedToken.id}`);

        res.json({
            status: 'success',
            token: token,
            tenant_url: savedToken.tenant_url,
            token_info: {
                id: savedToken.id,
                created_at: savedToken.created_at
            }
        });

        logger.info('OAuth授权成功完成');

    } catch (error) {
        logger.error('OAuth回调处理失败:', error.message);
        res.status(500).json({ error: error.message });
    }
}

module.exports = {
    getAccessToken,
    setAuthInfo,
    getAuthInfo,
    handleCallback
};
