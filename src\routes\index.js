const authRoutes = require('./auth');
const oauthRoutes = require('./oauth');
const apiRoutes = require('./api');
const externalRoutes = require('./external');
const externalV1Routes = require('./external-v1');
const config = require('../config');

function setupRoutes(app) {
    // 外部API路由（保留原有OAuth接口）- 必须在版本化路由之前
    app.use('/api/external', externalRoutes);

    // 版本化外部API路由（版本认证）
    app.use('/api/external/:version', externalV1Routes);

    // 认证相关路由
    app.use('/', authRoutes);

    // OAuth相关路由
    app.use('/', oauthRoutes);

    // API路由
    const routePrefix = config.get('ROUTE_PREFIX') || '';
    app.use(routePrefix, apiRoutes);
}

module.exports = setupRoutes;
