const config = require('../config');
const logger = require('../logger');

/**
 * 外部API版本认证中间件
 */
function versionAuthMiddleware(req, res, next) {
    try {
        // 从URL路径中提取版本号
        const version = req.params.version;
        
        if (!version) {
            return res.status(400).json({
                success: false,
                error: '缺少API版本号',
                supported_versions: getSupportedVersions()
            });
        }
        
        // 获取版本配置
        const versionConfig = config.get(`external_api.versions.${version}`);
        
        if (!versionConfig) {
            return res.status(400).json({
                success: false,
                error: `不支持的API版本: ${version}`,
                supported_versions: getSupportedVersions()
            });
        }
        
        if (!versionConfig.enabled) {
            return res.status(400).json({
                success: false,
                error: `API版本 ${version} 已禁用`,
                supported_versions: getSupportedVersions()
            });
        }
        
        // 检查API Key（可选）
        const apiKey = req.headers['x-api-key'];
        if (versionConfig.api_key && apiKey !== versionConfig.api_key) {
            logger.warn(`版本认证失败: 版本=${version}, 提供的key=${apiKey ? apiKey.substring(0, 10) + '...' : 'empty'}`);
            return res.status(401).json({
                success: false,
                error: 'Invalid API key for this version'
            });
        }
        
        // 将版本信息添加到请求对象
        req.apiVersion = version;
        req.versionConfig = versionConfig;
        
        logger.info(`版本认证成功: 版本=${version}, IP=${req.ip}`);
        next();
        
    } catch (error) {
        logger.error('版本认证中间件错误:', error.message);
        res.status(500).json({
            success: false,
            error: '版本认证失败',
            details: error.message
        });
    }
}

/**
 * 获取支持的版本列表
 */
function getSupportedVersions() {
    try {
        const versions = config.get('external_api.versions') || {};
        return Object.keys(versions).filter(v => versions[v].enabled);
    } catch (error) {
        return [];
    }
}

/**
 * 获取默认版本
 */
function getDefaultVersion() {
    return config.get('external_api.default_version') || 'v1';
}

module.exports = {
    versionAuthMiddleware,
    getSupportedVersions,
    getDefaultVersion
};
