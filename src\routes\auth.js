const express = require('express');
const path = require('path');
const AuthController = require('../controllers/auth');
const { jwtAuthMiddleware, checkLoginStatus } = require('../middleware/auth');

const router = express.Router();

// 登录页面
router.get('/login', checkLoginStatus, AuthController.showLoginPage);

// 登录API
router.post('/api/login', AuthController.login);

// 登出API
router.post('/api/logout', jwtAuthMiddleware, AuthController.logout);

// 获取当前用户信息
router.get('/api/user', jwtAuthMiddleware, AuthController.getCurrentUser);

// 获取用户的token列表
router.get('/api/user/tokens', jwtAuthMiddleware, AuthController.getUserTokens);

// 获取用户的可用token列表（user_ck为空的）
router.get('/api/user/available-tokens', jwtAuthMiddleware, AuthController.getUserAvailableTokens);

// 刷新token
router.post('/api/refresh-token', AuthController.refreshToken);

// 主页（需要登录）
router.get('/', jwtAuthMiddleware, (req, res) => {
    res.sendFile(path.join(__dirname, '../../public', 'index.html'));
});

// 个人中心页面（需要登录）
router.get('/profile', jwtAuthMiddleware, (req, res) => {
    res.sendFile(path.join(__dirname, '../../public', 'profile.html'));
});

// API文档页面（需要登录）
router.get('/api-docs', jwtAuthMiddleware, (req, res) => {
    res.sendFile(path.join(__dirname, '../../public', 'api-docs.html'));
});

// 外部接口测试页面（需要登录）
router.get('/external-test', jwtAuthMiddleware, (req, res) => {
    res.sendFile(path.join(__dirname, '../../public', 'external-api-test.html'));
});



module.exports = router;
