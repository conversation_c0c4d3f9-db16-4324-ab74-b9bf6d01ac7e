<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面标题 - Augment2API</title>
    
    <!-- 引入导航组件样式 -->
    <link rel="stylesheet" href="/components/nav.css">
    
    <!-- 页面特定样式 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .main-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .content {
            line-height: 1.6;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="app-nav"></div>
    
    <!-- 页面内容 -->
    <div class="container">
        <div class="main-card">
            <h1>页面标题</h1>
            <div class="content">
                <p>这里是页面内容...</p>
            </div>
        </div>
    </div>

    <!-- 引入导航组件脚本 -->
    <script src="/components/nav.js"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        // 页面加载完成后初始化导航栏
        window.addEventListener('load', async () => {
            // 初始化导航栏，可以传入配置参数
            await AppNav.init({
                showUserInfo: true,
                showProfileBtn: true,
                showHomeBtn: true,
                customButtons: [
                    // 可以添加自定义按钮
                    // {
                    //     text: '自定义按钮',
                    //     href: '/custom-page',
                    //     type: 'primary',
                    //     icon: '⚙️'
                    // }
                ]
            });
            
            // 页面特定的初始化逻辑
            initPage();
        });
        
        function initPage() {
            // 页面特定的初始化代码
            console.log('页面初始化完成');
            
            // 获取当前用户信息
            const currentUser = AppNav.getCurrentUser();
            if (currentUser) {
                console.log('当前用户:', currentUser);
            }
        }
    </script>
</body>
</html>
