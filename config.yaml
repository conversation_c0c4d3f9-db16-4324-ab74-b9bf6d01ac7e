# Augment2API 配置文件
app:
  port: 27080

# 数据库配置http://************:8888/database/mysql
database:
  mysql:
    # host: "localhost"
    host: "************"
    port: 3306
    user: "augmenttoken"
    password: "augmenttoken"
    database: "augmenttoken"
    charset: "utf8"
    connectionLimit: 10

# JWT配置
jwt:
  secret: "augment2api_secret_key_2024_very_secure"
  expires_in: "7d"

# 外部API版本认证配置
external_api:
  versions:
    v1:
      enabled: true
      api_key: "augment_external_v1_2024"
      description: "Token管理接口版本1"
  default_version: "v1"

