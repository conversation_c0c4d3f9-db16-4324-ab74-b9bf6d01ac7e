# Token管理接口 v1

## 🔑 认证信息
- **API Key:** `augment_external_v1_2024`
- **认证方式:** 请求头 `X-API-Key`
- **服务地址:** `http://localhost:27080`

---

## 📋 接口列表

### 1. 查询可用Token列表

**接口地址:** `GET /api/external/v1/tokens`

**请求头:**
```
X-API-Key: augment_external_v1_2024
```

**查询参数:**
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| limit | integer | 否 | 10 | 每页数量（最大100） |

**请求示例:**
```bash
GET /api/external/v1/tokens?page=1&limit=20
X-API-Key: augment_external_v1_2024
```

**响应示例:**
```json
{
  "success": true,
  "tokens": [
    {
      "id": 36,
      "token": "1c299f47335142a4179d51cf026883fd4cfc2a9cf56ce8a6ad516d6aa8eb04c1",
      "tenant_url": "https://d20.api.augmentcode.com/",
      "use_time": null,
      "created_at": "2025-08-07 10:23:25",
      "updated_at": "2025-08-07 10:23:25"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": "21",
    "totalPages": 3
  }
}
```

**说明:**
- 只返回 `user_ck` 为空且未销毁的Token
- 按创建时间倒序排列

---

### 2. 修改Token的user_ck

**接口地址:** `PUT /api/external/v1/tokens`

**请求头:**
```
X-API-Key: augment_external_v1_2024
Content-Type: application/json
```

**请求体:**
```json
{
  "token": "完整的token值",
  "user_ck": "用户标识符"
}
```

**请求示例:**
```bash
PUT /api/external/v1/tokens
X-API-Key: augment_external_v1_2024
Content-Type: application/json

{
  "token": "1c299f47335142a4179d51cf026883fd4cfc2a9cf56ce8a6ad516d6aa8eb04c1",
  "user_ck": "test_v1_user_123"
}
```

**响应示例:**
```json
{
  "success": true,
  "message": "Token user_ck 更新成功",
  "user_ck": "test_v1_user_123"
}
```

**参数说明:**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| token | string | 是 | 完整的访问令牌值 |
| user_ck | string | 是 | 用户标识符（非空字符串） |

---

## 🚨 错误响应

```json
{
  "success": false,
  "error": "错误描述",
  "details": "详细错误信息"
}
```

**常见错误:**
- `400` - 请求参数错误
- `401` - API Key无效
- `404` - Token不存在
- `500` - 服务器错误

---

## 📝 cURL示例

```bash
# 查询Token列表
curl -X GET "http://localhost:27080/api/external/v1/tokens?page=1&limit=10" \
  -H "X-API-Key: augment_external_v1_2024"

# 修改Token
curl -X PUT "http://localhost:27080/api/external/v1/tokens" \
  -H "X-API-Key: augment_external_v1_2024" \
  -H "Content-Type: application/json" \
  -d '{
    "token": "1c299f47335142a4179d51cf026883fd4cfc2a9cf56ce8a6ad516d6aa8eb04c1",
    "user_ck": "test_user_123"
  }'
```

---

**文档版本:** v1.0  
**最后更新:** 2025-08-07
