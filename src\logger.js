class Logger {
    constructor() {
        this.isDebug = process.env.DEBUG === 'true';
    }
    
    formatMessage(level, message, ...args) {
        const timestamp = new Date().toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const formattedMessage = typeof message === 'string' ? message : JSON.stringify(message);
        const extraArgs = args.length > 0 ? ' ' + args.map(arg => 
            typeof arg === 'string' ? arg : JSON.stringify(arg)
        ).join(' ') : '';
        
        return `[${timestamp}] [${level}] ${formattedMessage}${extraArgs}`;
    }
    
    info(message, ...args) {
        console.log(this.formatMessage('INFO', message, ...args));
    }
    
    error(message, ...args) {
        console.error(this.formatMessage('ERROR', message, ...args));
    }
    
    warn(message, ...args) {
        console.warn(this.formatMessage('WARN', message, ...args));
    }
    
    debug(message, ...args) {
        if (this.isDebug) {
            console.log(this.formatMessage('DEBUG', message, ...args));
        }
    }
    
    withFields(fields) {
        return {
            info: (message, ...args) => {
                this.info(message, JSON.stringify(fields), ...args);
            },
            error: (message, ...args) => {
                this.error(message, JSON.stringify(fields), ...args);
            },
            warn: (message, ...args) => {
                this.warn(message, JSON.stringify(fields), ...args);
            },
            debug: (message, ...args) => {
                this.debug(message, JSON.stringify(fields), ...args);
            }
        };
    }
}

module.exports = new Logger();
