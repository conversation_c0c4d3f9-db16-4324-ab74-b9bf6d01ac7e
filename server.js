const express = require('express');
const cors = require('cors');
const cookieParser = require('cookie-parser');

// 导入模块
const config = require('./src/config');
const logger = require('./src/logger');
const database = require('./src/database');
const setupRoutes = require('./src/routes');

const app = express();

// 跨域配置 - 允许所有请求
const corsOptions = {
    origin: function (origin, callback) {
        // 允许所有来源，包括没有origin的请求（如Postman）
        callback(null, true);
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'], // 允许所有HTTP方法
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'], // 允许的请求头
    credentials: true, // 允许携带凭证（cookies等）
    optionsSuccessStatus: 200, // 对于某些旧版浏览器的兼容性
    preflightContinue: false // 不继续传递预检请求到下一个处理器
};

// 中间件
app.use(cors(corsOptions));

// 全局CORS中间件 - 确保所有响应都包含CORS头
app.use((req, res, next) => {
    const origin = req.headers.origin || '*';
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS,PATCH');
    res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Accept,Origin');
    res.header('Access-Control-Allow-Credentials', 'true');
    next();
});

app.use(express.json({ limit: '10mb' })); // 增加请求体大小限制
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // 支持URL编码的请求体
app.use(cookieParser());
app.use(express.static('public'));

// 处理预检请求
app.options('*', (req, res) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS,PATCH');
    res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With,Accept,Origin');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.sendStatus(200);
});

// 全局变量存储OAuth状态和token信息
global.oauthState = null;
global.accessToken = null;
global.tenantURL = null;

// 启动服务器
async function startServer() {
    try {
        // 初始化配置
        config.init();

        // 初始化数据库
        await initDatabase();

        // 设置路由
        setupRoutes(app);

        // 获取端口配置
        const port = config.get('app.port') || 27080;

        // 启动服务器
        app.listen(port, () => {
            logger.info(`Augment2API 服务启动成功!`);
            logger.info(`端口: ${port}`);
            logger.info(`访问地址: http://localhost:${port}`);

            // 显示数据库连接状态
            const dbStatus = database.getPoolStatus();
            if (dbStatus) {
                logger.info(`数据库连接池: ${dbStatus.totalConnections}/${dbStatus.connectionLimit} (空闲: ${dbStatus.freeConnections})`);
            }
        });

    } catch (error) {
        logger.error('服务启动失败:', error.message);
        process.exit(1);
    }
}

// 初始化数据库
async function initDatabase() {
    try {
        const dbConfig = config.getDatabase();

        if (!dbConfig) {
            logger.warn('未找到数据库配置，跳过数据库初始化');
            return;
        }

        logger.info('正在初始化数据库连接...');

        // 检查数据库是否存在，如果不存在则创建
        const dbExists = await database.checkDatabase(dbConfig);
        if (!dbExists) {
            logger.info(`数据库 ${dbConfig.database} 不存在，正在创建...`);
            await database.createDatabase(dbConfig);
        }

        // 初始化数据库连接池
        await database.init(dbConfig);

        logger.info('数据库初始化完成');

    } catch (error) {
        logger.error('数据库初始化失败:', error.message);
        throw error;
    }
}

// 错误处理
process.on('uncaughtException', (err) => {
    logger.error('未捕获的异常:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason) => {
    logger.error('未处理的Promise拒绝:', reason);
});

// 优雅关闭处理
process.on('SIGTERM', async () => {
    logger.info('收到SIGTERM信号，正在优雅关闭...');
    await gracefulShutdown();
});

process.on('SIGINT', async () => {
    logger.info('收到SIGINT信号，正在优雅关闭...');
    await gracefulShutdown();
});

async function gracefulShutdown() {
    try {
        // 关闭数据库连接
        await database.close();
        logger.info('应用已优雅关闭');
        process.exit(0);
    } catch (error) {
        logger.error('优雅关闭失败:', error.message);
        process.exit(1);
    }
}

// 启动应用
startServer();

module.exports = app;
