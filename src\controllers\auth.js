const path = require('path');
const User = require('../models/User');
const JWTUtils = require('../utils/jwt');
const logger = require('../logger');
const database = require('../database');

class AuthController {
    // 显示登录页面
    static showLoginPage(req, res) {
        res.sendFile(path.join(__dirname, '../../public', 'login.html'));
    }
    
    // 处理登录请求
    static async login(req, res) {
        try {
            const { email } = req.body;
            
            // 验证邮箱格式
            if (!email || !AuthController.isValidEmail(email)) {
                return res.status(400).json({
                    error: '请输入有效的邮箱地址'
                });
            }
            
            // 查找或创建用户
            const user = await User.findOrCreate(email);
            
            // 生成短token
            const tokenPayload = {
                uuid: user.uuid,
                email: user.email
            };

            const token = JWTUtils.generateToken(tokenPayload);

            // 将token保存到数据库
            await JWTUtils.updateUserToken(user.uuid, token);

            // 设置Cookie（可选）
            res.cookie('auth_token', token, {
                httpOnly: true,
                secure: false, // 开发环境设为false
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
                sameSite: 'lax'
            });
            
            logger.info(`用户登录成功: ${email}`);
            
            res.json({
                success: true,
                message: '登录成功',
                token: token,
                user: user.toJSON(),
                redirect: '/'
            });
            
        } catch (error) {
            logger.error('登录处理失败:', error.message);
            
            res.status(500).json({
                error: '登录失败，请稍后重试'
            });
        }
    }
    
    // 处理登出请求
    static async logout(req, res) {
        try {
            // 清除Cookie
            res.clearCookie('auth_token');
            
            logger.info(`用户登出: ${req.user?.email || '未知'}`);
            
            res.json({
                success: true,
                message: '登出成功',
                redirect: '/login'
            });
            
        } catch (error) {
            logger.error('登出处理失败:', error.message);
            
            res.status(500).json({
                error: '登出失败'
            });
        }
    }
    
    // 获取当前用户信息
    static async getCurrentUser(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    error: '未登录'
                });
            }

            // 从数据库获取最新用户信息
            const user = await User.findByEmail(req.user.email);

            if (!user) {
                return res.status(404).json({
                    error: '用户不存在'
                });
            }

            res.json({
                success: true,
                user: user.toJSON()
            });

        } catch (error) {
            logger.error('获取用户信息失败:', error.message);

            res.status(500).json({
                error: '获取用户信息失败'
            });
        }
    }

    // 获取用户的token列表
    static async getUserTokens(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    error: '未登录'
                });
            }

            // 查询用户创建的token
            const sql = `
                SELECT id, token, tenant_url, user_ck, use_time, destroy_time, created_at, updated_at
                FROM tokens
                WHERE creator_email = ?
                ORDER BY created_at DESC
            `;

            const { rows } = await database.query(sql, [req.user.email]);

            // 处理token数据，隐藏敏感信息
            const tokens = rows.map(token => ({
                id: token.id,
                token: token.token, // 显示完整token
                tenant_url: token.tenant_url,
                user_ck: token.user_ck,
                use_time: token.use_time,
                destroy_time: token.destroy_time,
                created_at: token.created_at,
                updated_at: token.updated_at,
                is_active: !token.destroy_time
            }));

            res.json({
                success: true,
                tokens: tokens,
                total: tokens.length
            });

        } catch (error) {
            logger.error('获取用户token失败:', error.message);

            res.status(500).json({
                error: '获取token列表失败'
            });
        }
    }
    
    // 刷新token
    static async refreshToken(req, res) {
        try {
            const oldToken = JWTUtils.extractTokenFromRequest(req);
            
            if (!oldToken) {
                return res.status(401).json({
                    error: '未提供token'
                });
            }
            
            const newToken = JWTUtils.refreshToken(oldToken);
            
            // 更新Cookie
            res.cookie('auth_token', newToken, {
                httpOnly: true,
                secure: false, // 开发环境设为false
                maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
                sameSite: 'lax'
            });
            
            res.json({
                success: true,
                token: newToken,
                message: 'Token刷新成功'
            });
            
        } catch (error) {
            logger.error('Token刷新失败:', error.message);
            
            res.status(401).json({
                error: 'Token刷新失败',
                redirect: '/login'
            });
        }
    }
    
    // 获取用户的可用token列表（user_ck为空的）
    static async getUserAvailableTokens(req, res) {
        try {
            if (!req.user) {
                return res.status(401).json({
                    error: '未登录'
                });
            }

            const { page = 1, limit = 10 } = req.query;

            // 验证分页参数
            const pageNum = Math.max(1, parseInt(page));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit))); // 限制最大100条

            // 查询当前用户创建的且user_ck为空的可用Token
            const Token = require('../models/Token');
            const result = await Token.findAvailableWithoutUserCk({
                page: pageNum,
                limit: limitNum,
                creatorEmail: req.user.email // 限制为当前用户创建的
            });

            // 处理返回数据
            const tokens = result.tokens.map(token => ({
                id: token.id,
                token: token.token,
                tenant_url: token.tenant_url,
                use_time: token.use_time,
                created_at: token.created_at,
                updated_at: token.updated_at
            }));

            res.json({
                success: true,
                tokens: tokens,
                pagination: result.pagination,
                user: {
                    email: req.user.email
                }
            });

        } catch (error) {
            logger.error('获取用户可用Token失败:', error.message);

            res.status(500).json({
                error: '获取Token列表失败'
            });
        }
    }

    // 验证邮箱格式
    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
}

module.exports = AuthController;
