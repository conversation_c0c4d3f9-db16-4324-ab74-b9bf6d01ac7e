const mysql = require('mysql2/promise');
const logger = require('./logger');

class Database {
    constructor() {
        this.pool = null;
        this.config = null;
    }
    
    // 初始化数据库连接
    async init(dbConfig) {
        try {
            this.config = dbConfig;
            
            // 创建连接池
            this.pool = mysql.createPool({
                host: dbConfig.host,
                port: dbConfig.port,
                user: dbConfig.user,
                password: dbConfig.password,
                database: dbConfig.database,
                charset: dbConfig.charset || 'utf8',
                timezone: '+08:00',
                connectionLimit: dbConfig.connectionLimit || 10,
                ssl: dbConfig.ssl || false,
                // 连接配置
                dateStrings: true,
                supportBigNumbers: true,
                bigNumberStrings: true,
                // 等待连接超时
                acquireTimeout: 60000,
                // 查询超时
                timeout: 60000
            });
            
            // 测试连接
            await this.testConnection();
            
            logger.info('MySQL数据库连接池初始化成功');
            logger.info(`数据库: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
            
        } catch (error) {
            logger.error('MySQL数据库初始化失败:', error.message);
            throw error;
        }
    }
    
    // 测试数据库连接
    async testConnection() {
        try {
            const connection = await this.pool.getConnection();
            await connection.ping();
            connection.release();
            logger.info('数据库连接测试成功');
        } catch (error) {
            logger.error('数据库连接测试失败:', error.message);
            throw error;
        }
    }
    
    // 执行查询
    async query(sql, params = []) {
        try {
            const [rows, fields] = await this.pool.execute(sql, params);
            return { rows, fields };
        } catch (error) {
            logger.error('数据库查询失败:', {
                sql: sql,
                params: params,
                error: error.message
            });
            throw error;
        }
    }
    
    // 执行事务
    async transaction(callback) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();
            const result = await callback(connection);
            await connection.commit();
            return result;
        } catch (error) {
            await connection.rollback();
            logger.error('数据库事务失败:', error.message);
            throw error;
        } finally {
            connection.release();
        }
    }
    
    // 检查数据库是否存在
    async checkDatabase(dbConfig) {
        try {
            // 如果没有传入配置，使用实例配置
            const config = dbConfig || this.config;
            if (!config) {
                logger.error('数据库配置为空');
                return false;
            }

            // 临时连接，不指定数据库
            const tempPool = mysql.createPool({
                host: config.host,
                port: config.port,
                user: config.user,
                password: config.password,
                charset: config.charset || 'utf8'
            });

            const sql = 'SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?';
            const [rows] = await tempPool.execute(sql, [config.database]);
            await tempPool.end();

            return rows.length > 0;
        } catch (error) {
            logger.error('检查数据库失败:', error.message);
            return false;
        }
    }
    
    // 创建数据库
    async createDatabase(dbConfig) {
        try {
            // 如果没有传入配置，使用实例配置
            const config = dbConfig || this.config;
            if (!config) {
                throw new Error('数据库配置为空');
            }

            // 临时连接，不指定数据库
            const tempPool = mysql.createPool({
                host: config.host,
                port: config.port,
                user: config.user,
                password: config.password,
                charset: config.charset || 'utf8'
            });

            const sql = `CREATE DATABASE IF NOT EXISTS \`${config.database}\`
                        CHARACTER SET utf8 COLLATE utf8_general_ci`;

            await tempPool.execute(sql);
            await tempPool.end();

            logger.info(`数据库 ${config.database} 创建成功`);
        } catch (error) {
            logger.error('创建数据库失败:', error.message);
            throw error;
        }
    }
    
    // 检查表是否存在
    async checkTable(tableName) {
        try {
            const sql = `SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?`;
            const { rows } = await this.query(sql, [this.config.database, tableName]);
            return rows.length > 0;
        } catch (error) {
            logger.error(`检查表 ${tableName} 失败:`, error.message);
            return false;
        }
    }
    
    // 获取连接池状态
    getPoolStatus() {
        if (!this.pool) {
            return null;
        }

        try {
            return {
                totalConnections: this.pool._allConnections ? this.pool._allConnections.length : 0,
                freeConnections: this.pool._freeConnections ? this.pool._freeConnections.length : 0,
                acquiringConnections: this.pool._acquiringConnections ? this.pool._acquiringConnections.length : 0,
                connectionLimit: this.pool.config ? this.pool.config.connectionLimit : 10
            };
        } catch (error) {
            logger.error('获取连接池状态失败:', error.message);
            return {
                totalConnections: 0,
                freeConnections: 0,
                acquiringConnections: 0,
                connectionLimit: 10
            };
        }
    }
    
    // 关闭连接池
    async close() {
        if (this.pool) {
            await this.pool.end();
            logger.info('数据库连接池已关闭');
        }
    }
}

// 创建单例实例
const database = new Database();

module.exports = database;
