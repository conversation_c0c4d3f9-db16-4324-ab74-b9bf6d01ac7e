const express = require('express');
const router = express.Router({ mergeParams: true });
const ExternalV1Controller = require('../controllers/external-v1');
const { versionAuthMiddleware } = require('../middleware/version');

// 版本认证中间件
router.use(versionAuthMiddleware);

/**
 * GET /api/external/v1/tokens
 * 查询可用Token列表（user_ck为空的Token）
 * 
 * Headers:
 *   X-API-Key: augment_external_v1_2024 (可选)
 * 
 * Query Parameters:
 *   page: 页码，默认1
 *   limit: 每页数量，默认10，最大100
 * 
 * Response:
 *   {
 *     "success": true,
 *     "tokens": [
 *       {
 *         "id": 123,
 *         "token": "access_token_value",
 *         "tenant_url": "https://tenant.augmentcode.com",
 *         "use_time": "2025-01-01T00:00:00Z",
 *         "created_at": "2025-01-01T00:00:00Z",
 *         "updated_at": "2025-01-01T00:00:00Z"
 *       }
 *     ],
 *     "pagination": {
 *       "page": 1,
 *       "limit": 10,
 *       "total": 25,
 *       "totalPages": 3
 *     }
 *   }
 */
router.get('/tokens', ExternalV1Controller.getAvailableTokens);

/**
 * PUT /api/external/v1/tokens
 * 根据token值修改user_ck
 * 
 * Headers:
 *   Content-Type: application/json
 *   X-API-Key: augment_external_v1_2024 (可选)
 * 
 * Body:
 *   {
 *     "token": "access_token_value",
 *     "user_ck": "user_identifier"
 *   }
 * 
 * Response:
 *   {
 *     "success": true,
 *     "message": "Token user_ck 更新成功",
 *     "user_ck": "user_identifier"
 *   }
 */
router.put('/tokens', ExternalV1Controller.updateTokenUserCk);

module.exports = router;
