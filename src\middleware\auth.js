const JWTUtils = require('../utils/jwt');
const User = require('../models/User');
const logger = require('../logger');

// Token认证中间件（异步）
async function jwtAuthMiddleware(req, res, next) {
    try {
        // 提取token
        const token = JWTUtils.extractTokenFromRequest(req);

        if (!token) {
            // 如果是页面请求，直接重定向
            if (req.accepts('html')) {
                return res.redirect('/login');
            }
            // 如果是API请求，返回JSON
            return res.status(401).json({
                error: '未提供认证token',
                redirect: '/login'
            });
        }

        // 验证token（异步查询数据库）
        const decoded = await JWTUtils.verifyToken(token);

        // 将用户信息添加到请求对象
        req.user = {
            uuid: decoded.uuid,
            email: decoded.email
        };

        next();

    } catch (error) {
        logger.error('Token认证失败:', error.message);

        // 如果是页面请求，直接重定向
        if (req.accepts('html')) {
            return res.redirect('/login');
        }

        // 如果是API请求，返回JSON
        if (error.message.includes('过期')) {
            return res.status(401).json({
                error: 'Token已过期，请重新登录',
                redirect: '/login'
            });
        }

        return res.status(401).json({
            error: '认证失败',
            redirect: '/login'
        });
    }
}

// 可选的Token认证中间件（不强制要求登录）
async function optionalJwtAuthMiddleware(req, res, next) {
    try {
        const token = JWTUtils.extractTokenFromRequest(req);

        if (token) {
            const decoded = await JWTUtils.verifyToken(token);
            req.user = {
                uuid: decoded.uuid,
                email: decoded.email
            };
        }

        next();

    } catch (error) {
        // 可选认证失败不阻止请求继续
        logger.warn('可选Token认证失败:', error.message);
        next();
    }
}

// 检查用户是否已登录
async function checkLoginStatus(req, res, next) {
    const token = JWTUtils.extractTokenFromRequest(req);

    if (token) {
        try {
            await JWTUtils.verifyToken(token);
            // 已登录，重定向到主页
            return res.redirect('/');
        } catch (error) {
            // token无效，继续到登录页面
        }
    }

    next();
}

module.exports = {
    jwtAuthMiddleware,
    optionalJwtAuthMiddleware,
    checkLoginStatus
};
