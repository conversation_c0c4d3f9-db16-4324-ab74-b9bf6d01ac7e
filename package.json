{"name": "augment2api-node", "version": "1.0.0", "description": "A simplified proxy service that converts Augment AI into an OpenAI-compatible API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["augment", "openai", "api", "o<PERSON>h", "proxy"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "crypto": "^1.0.1", "uuid": "^9.0.1", "mysql2": "^3.6.5", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}