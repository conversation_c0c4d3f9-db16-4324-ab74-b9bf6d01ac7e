/**
 * 统一导航栏组件
 * 使用方法：
 * 1. 在HTML中引入nav.css和nav.js
 * 2. 在页面加载时调用 AppNav.init()
 * 3. 可选：传入配置参数自定义导航
 */

class AppNav {
    constructor() {
        this.currentUser = null;
        this.isLoading = false;
    }

    // 生成导航HTML
    generateNavHTML(config = {}) {
        const {
            showUserInfo = true,
            showProfileBtn = true,
            showHomeBtn = true,
            customButtons = []
        } = config;

        return `
            <div class="header">
                <div class="header-content">
                    <a href="/" class="logo">
                        🔐 Augment2API
                    </a>
                    <div class="nav-buttons">
                        ${showUserInfo && this.currentUser ? this.generateUserInfo() : ''}
                        ${showHomeBtn ? '<a href="/" class="nav-btn primary">🏠 主页</a>' : ''}
                        ${showProfileBtn && this.currentUser ? '<a href="/profile" class="nav-btn primary">👤 个人中心</a>' : ''}
                        ${customButtons.map(btn => this.generateCustomButton(btn)).join('')}
                        ${this.currentUser ? '<button onclick="AppNav.logout()" class="nav-btn secondary">🚪 登出</button>' : '<a href="/login" class="nav-btn primary">🔑 登录</a>'}
                    </div>
                </div>
            </div>
        `;
    }

    // 生成用户信息HTML
    generateUserInfo() {
        if (!this.currentUser) return '';
        
        const avatar = this.currentUser.email ? this.currentUser.email.charAt(0).toUpperCase() : '?';
        return `
            <div class="user-info">
                <div class="user-avatar">${avatar}</div>
                <span>欢迎，${this.currentUser.email}</span>
            </div>
        `;
    }

    // 生成自定义按钮HTML
    generateCustomButton(button) {
        const { text, href, onclick, type = 'secondary', icon = '' } = button;
        const clickHandler = onclick ? `onclick="${onclick}"` : '';
        const link = href ? `href="${href}"` : 'href="#"';
        
        return `<a ${link} ${clickHandler} class="nav-btn ${type}">${icon} ${text}</a>`;
    }

    // 检查登录状态
    async checkLoginStatus() {
        if (this.isLoading) return false;

        this.isLoading = true;
        try {
            const response = await fetch('/api/user');
            if (response.ok) {
                const result = await response.json();
                this.currentUser = result.user;
                return true;
            } else if (response.status === 401) {
                // Token无效或过期，跳转到登录页
                this.currentUser = null;
                console.log('用户未登录或token已过期，跳转到登录页');
                window.location.href = '/login';
                return false;
            } else {
                this.currentUser = null;
                return false;
            }
        } catch (error) {
            console.error('检查登录状态失败:', error);
            this.currentUser = null;
            return false;
        } finally {
            this.isLoading = false;
        }
    }

    // 登出功能
    async logout() {
        try {
            const response = await fetch('/api/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                // 清除本地存储的token
                localStorage.removeItem('auth_token');
                // 跳转到登录页
                window.location.href = '/login';
            } else {
                console.error('登出失败');
                alert('登出失败，请重试');
            }
        } catch (error) {
            console.error('登出错误:', error);
            alert('网络错误，请重试');
        }
    }

    // 渲染导航栏
    async render(containerId = 'app-nav', config = {}) {
        // 检查登录状态
        await this.checkLoginStatus();
        
        // 生成HTML
        const navHTML = this.generateNavHTML(config);
        
        // 插入到页面
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = navHTML;
        } else {
            // 如果没有指定容器，插入到body开头
            document.body.insertAdjacentHTML('afterbegin', navHTML);
        }
    }

    // 更新用户信息显示
    updateUserInfo(user) {
        this.currentUser = user;
        // 重新渲染导航栏
        this.render();
    }

    // 静态方法：初始化导航栏
    static async init(config = {}) {
        if (!window.appNav) {
            window.appNav = new AppNav();
        }
        await window.appNav.render('app-nav', config);
        return window.appNav;
    }

    // 静态方法：登出
    static async logout() {
        if (window.appNav) {
            await window.appNav.logout();
        }
    }

    // 静态方法：获取当前用户
    static getCurrentUser() {
        return window.appNav ? window.appNav.currentUser : null;
    }
}

// 导出到全局
window.AppNav = AppNav;
