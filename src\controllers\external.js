const crypto = require('crypto');
const logger = require('../logger');
const Token = require('../models/Token');

// 存储OAuth状态（按用户分组）
const userOAuthStates = new Map();

class ExternalController {
    // 创建OAuth状态（与现有逻辑一致）
    static createOAuthState() {
        const codeVerifierBytes = crypto.randomBytes(32);
        const codeVerifier = codeVerifierBytes.toString('base64url');
        const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
        const state = crypto.randomBytes(8).toString('base64url');

        return {
            codeVerifier,
            codeChallenge,
            state,
            creationTime: new Date()
        };
    }

    // 生成授权URL（与现有逻辑一致）
    static generateAuthorizeURL(oauthState) {
        const params = new URLSearchParams({
            response_type: 'code',
            code_challenge: oauthState.codeChallenge,
            client_id: 'v',
            state: oauthState.state,
            prompt: 'login'
        });

        return `https://auth.augmentcode.com/authorize?${params.toString()}`;
    }

    // 获取OAuth授权链接（与现有逻辑完全一致）
    static async getAuthUrl(req, res) {
        try {
            // 用户信息已通过中间件验证并添加到req.user
            const user = req.user;
            logger.info(`外部API请求获取授权链接: ${user.email}`);

            // 创建OAuth状态（与现有逻辑一致）
            const oauthState = ExternalController.createOAuthState();

            // 生成授权URL（与现有逻辑一致）
            const authorizeURL = ExternalController.generateAuthorizeURL(oauthState);

            // 保存用户的OAuth状态
            userOAuthStates.set(user.email, oauthState);

            logger.info(`授权链接生成成功: ${user.email} -> ${authorizeURL}`);

            // 返回格式与内部接口完全一致
            res.json({
                authorize_url: authorizeURL
            });

        } catch (error) {
            logger.error('获取授权链接失败:', error.message);
            res.status(500).json({
                success: false,
                error: '获取授权链接失败',
                details: error.message
            });
        }
    }

    // 获取访问令牌（与现有逻辑一致）
    static async getAccessToken(tenantURL, codeVerifier, code) {
        try {
            const axios = require('axios');
            const data = {
                grant_type: 'authorization_code',
                client_id: 'v',
                code_verifier: codeVerifier,
                redirect_uri: '',
                code: code
            };

            const response = await axios.post(tenantURL + 'token', data, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.data && response.data.access_token) {
                return response.data.access_token;
            } else {
                throw new Error('响应中没有访问令牌');
            }
        } catch (error) {
            if (error.response) {
                throw new Error(`请求令牌失败: ${error.response.status} ${error.response.statusText}`);
            } else {
                throw new Error(`请求令牌失败: ${error.message}`);
            }
        }
    }

    // 完成OAuth授权（与现有逻辑一致）
    static async completeAuth(req, res) {
        try {
            // 用户信息已通过中间件验证
            const user = req.user;
            const { code, state, tenant_url } = req.body;

            logger.info(`外部API请求完成授权: ${user.email}, code: ${code ? 'present' : 'missing'}`);

            // 验证必需参数（与现有逻辑一致）
            if (!code || !state || !tenant_url) {
                return res.status(400).json({
                    success: false,
                    error: '无效的请求数据'
                });
            }

            // 获取用户保存的OAuth状态
            const userOAuthState = userOAuthStates.get(user.email);
            if (!userOAuthState) {
                return res.status(400).json({
                    success: false,
                    error: '未找到OAuth状态，请重新获取授权链接'
                });
            }

            // 验证state参数
            if (userOAuthState.state !== state) {
                return res.status(400).json({
                    success: false,
                    error: '无效的state参数'
                });
            }

            // 使用授权码获取访问令牌（与现有逻辑一致）
            const token = await ExternalController.getAccessToken(
                tenant_url,
                userOAuthState.codeVerifier, // 使用保存的codeVerifier
                code
            );

            // 清除已使用的OAuth状态
            userOAuthStates.delete(user.email);

            // 保存token到数据库
            const savedToken = await Token.create({
                token: token,
                tenant_url: tenant_url,
                creator_email: user.email,
                user_ck: null // 外部接口创建的token没有user_ck
            });

            logger.info(`Token保存成功: ${user.email}, token_id: ${savedToken.id}`);

            res.json({
                success: true,
                status: 'success', // 兼容现有格式
                token: token,
                tenant_url: savedToken.tenant_url,
                token_info: {
                    id: savedToken.id,
                    created_at: savedToken.created_at
                },
                user: {
                    uuid: user.uuid,
                    email: user.email
                }
            });

        } catch (error) {
            logger.error('完成授权失败:', error.message);
            res.status(500).json({
                success: false,
                error: error.message
            });
        }
    }

}

module.exports = ExternalController;
