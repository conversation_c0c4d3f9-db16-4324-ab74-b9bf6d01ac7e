const express = require('express');
const router = express.Router();
const ExternalController = require('../controllers/external');
const { jwtAuthMiddleware } = require('../middleware/auth');

// 外部API接口 - 需要token认证

/**
 * GET /api/external/auth-url
 * 获取Augment OAuth授权链接
 *
 * Headers:
 *   Authorization: Bearer <short_token>
 *
 * Response:
 *   {
 *     "authorize_url": "https://auth.augmentcode.com/authorize?response_type=code&code_challenge=xxx&client_id=v&state=xxx&prompt=login"
 *   }
 */
router.get('/auth-url', jwtAuthMiddleware, ExternalController.getAuthUrl);

/**
 * POST /api/external/complete-auth
 * 完成OAuth授权，获取访问令牌
 * 
 * Headers:
 *   Authorization: Bearer <short_token>
 *   Content-Type: application/json
 * 
 * Body:
 *   {
 *     "code": "authorization_code",
 *     "state": "state_value",
 *     "tenant_url": "https://your-tenant.augmentcode.com" (可选)
 *   }
 * 
 * Response:
 *   {
 *     "success": true,
 *     "token": "access_token_value",
 *     "tenant_url": "https://tenant.augmentcode.com",
 *     "token_info": {
 *       "id": 123,
 *       "created_at": "2025-01-01T00:00:00Z"
 *     }
 *   }
 */
router.post('/complete-auth', jwtAuthMiddleware, ExternalController.completeAuth);

module.exports = router;
