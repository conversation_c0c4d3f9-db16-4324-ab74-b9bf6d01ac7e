<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - Augment2API</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #4a5568;
            margin-bottom: 10px;
            font-size: 1.8em;
        }
        
        .subtitle {
            color: #718096;
            margin-bottom: 30px;
            font-size: 0.95em;
        }
        
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 500;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        input[type="email"]:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .login-btn:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .loading {
            display: none;
            margin-left: 10px;
        }
        
        .loading.show {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #fed7d7;
            border: 1px solid #fc8181;
            color: #c53030;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .success-message {
            background: #f0fff4;
            border: 1px solid #68d391;
            color: #2f855a;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }
        
        .info {
            background: #ebf8ff;
            border: 1px solid #63b3ed;
            color: #2b6cb0;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🔐</div>
        <h1>欢迎登录</h1>
        <p class="subtitle">Augment2API 管理系统</p>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">邮箱地址</label>
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    required 
                    placeholder="请输入您的邮箱地址"
                    autocomplete="email"
                >
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                登录
                <span class="loading" id="loading"></span>
            </button>
        </form>
        
        <div class="info">
            <strong>💡 提示：</strong>
            <br>只需输入邮箱即可登录，系统会自动为新用户创建账户
        </div>
    </div>

    <script>
        const loginForm = document.getElementById('loginForm');
        const emailInput = document.getElementById('email');
        const loginBtn = document.getElementById('loginBtn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');

        // 隐藏消息
        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // 显示错误消息
        function showError(message) {
            hideMessages();
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        // 显示成功消息
        function showSuccess(message) {
            hideMessages();
            successMessage.textContent = message;
            successMessage.style.display = 'block';
        }

        // 设置加载状态
        function setLoading(isLoading) {
            if (isLoading) {
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中';
                loading.classList.add('show');
                loginBtn.appendChild(loading);
            } else {
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
                loading.classList.remove('show');
            }
        }

        // 处理登录表单提交
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = emailInput.value.trim();
            
            if (!email) {
                showError('请输入邮箱地址');
                return;
            }

            setLoading(true);
            hideMessages();

            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email })
                });

                const result = await response.json();

                if (response.ok) {
                    showSuccess('登录成功，正在跳转...');
                    
                    // 保存token到localStorage（可选）
                    if (result.token) {
                        localStorage.setItem('auth_token', result.token);
                    }
                    
                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = result.redirect || '/';
                    }, 1000);
                } else {
                    showError(result.error || '登录失败');
                }
            } catch (error) {
                showError('网络错误，请稍后重试');
                console.error('登录错误:', error);
            } finally {
                setLoading(false);
            }
        });

        // 页面加载时检查是否已登录
        window.addEventListener('load', () => {
            const token = localStorage.getItem('auth_token');
            if (token) {
                // 可以在这里验证token是否有效
                // 如果有效，直接跳转到主页
            }
        });
    </script>
</body>
</html>
