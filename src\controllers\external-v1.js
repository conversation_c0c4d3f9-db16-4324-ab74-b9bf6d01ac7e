const logger = require('../logger');
const Token = require('../models/Token');

class ExternalV1Controller {
    // 查询可用Token列表（user_ck为空的Token）
    static async getAvailableTokens(req, res) {
        try {
            const { page = 1, limit = 10 } = req.query;
            
            logger.info(`外部API v1请求查询可用Token: page=${page}, limit=${limit}, version=${req.apiVersion}`);
            
            // 验证分页参数
            const pageNum = Math.max(1, parseInt(page));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit))); // 限制最大100条
            
            // 查询所有user_ck为空的可用Token（不限制创建者）
            const result = await Token.findAvailableWithoutUserCk({
                page: pageNum,
                limit: limitNum,
                creatorEmail: null // 不限制创建者
            });
            
            // 处理返回数据，隐藏敏感信息
            const tokens = result.tokens.map(token => ({
                id: token.id,
                token: token.token,
                tenant_url: token.tenant_url,
                use_time: token.use_time,
                created_at: token.created_at,
                updated_at: token.updated_at
            }));
            
            logger.info(`查询可用Token成功: 返回${tokens.length}条记录, version=${req.apiVersion}`);
            
            res.json({
                success: true,
                tokens: tokens,
                pagination: result.pagination
            });
            
        } catch (error) {
            logger.error('查询可用Token失败:', error.message);
            res.status(500).json({
                success: false,
                error: '查询Token失败',
                details: error.message
            });
        }
    }
    
    // 根据token值修改user_ck
    static async updateTokenUserCk(req, res) {
        try {


            
            const { token, user_ck } = req.body;
            
            logger.info(`外部API v1请求修改Token user_ck: token=${token ? token.substring(0, 10) + '...' : 'empty'}, version=${req.apiVersion}`);
            
            // 验证必需参数
            if (!token || !user_ck) {
                return res.status(400).json({
                    success: false,
                    error: '缺少必需参数：token 和 user_ck'
                });
            }
            
            // 验证user_ck格式（可以根据需要添加更多验证）
            if (typeof user_ck !== 'string' || user_ck.trim().length === 0) {
                return res.status(400).json({
                    success: false,
                    error: 'user_ck 必须是非空字符串'
                });
            }
            
            // 更新Token的user_ck，不限制创建者
            await Token.updateUserCkByToken(token, user_ck.trim(), null);
            
            logger.info(`Token user_ck修改成功: user_ck=${user_ck}, version=${req.apiVersion}`);
            
            res.json({
                success: true,
                message: 'Token user_ck 更新成功',
                user_ck: user_ck.trim()
            });
            
        } catch (error) {
            logger.error('修改Token user_ck失败:', error.message);
            
            // 根据错误类型返回不同的状态码
            if (error.message.includes('不存在') || error.message.includes('无权限')) {
                res.status(404).json({
                    success: false,
                    error: error.message
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: '修改Token失败',
                    details: error.message
                });
            }
        }
    }
}

module.exports = ExternalV1Controller;
