const crypto = require('crypto');
const logger = require('../logger');
const config = require('../config');

class TokenUtils {
    // 生成短随机token
    static generateToken(payload) {
        try {
            // 生成8字节随机数，转为16位十六进制字符串
            const randomBytes = crypto.randomBytes(8);
            const token = `aug_${randomBytes.toString('hex')}`;

            logger.info(`短token生成成功: ${payload.email} -> ${token}`);
            return token;
        } catch (error) {
            logger.error('Token生成失败:', error.message);
            throw error;
        }
    }

    // 验证token（需要查询数据库）
    static async verifyToken(token) {
        try {
            const database = require('../database');

            // 查询数据库验证token
            const sql = `
                SELECT uuid, email, token_expires_at
                FROM users
                WHERE auth_token = ? AND (token_expires_at IS NULL OR token_expires_at > NOW())
            `;
            const { rows } = await database.query(sql, [token]);

            if (rows.length === 0) {
                throw new Error('Token无效或已过期');
            }

            const user = rows[0];
            logger.info(`Token验证成功: ${user.email}`);

            return {
                uuid: user.uuid,
                email: user.email
            };
        } catch (error) {
            logger.error('Token验证失败:', error.message);
            throw error;
        }
    }
    
    // 从请求中提取token
    static extractTokenFromRequest(req) {
        // 从Authorization header中提取
        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }

        // 从Cookie中提取
        if (req.cookies && req.cookies.auth_token) {
            return req.cookies.auth_token;
        }

        // 从查询参数中提取
        if (req.query.token) {
            return req.query.token;
        }

        return null;
    }

    // 刷新token（生成新的随机token）
    static async refreshToken(oldToken) {
        try {
            // 先验证旧token
            const user = await TokenUtils.verifyToken(oldToken);

            // 生成新token
            const newToken = TokenUtils.generateToken(user);

            // 更新数据库中的token
            await TokenUtils.updateUserToken(user.uuid, newToken);

            logger.info(`Token刷新成功: ${user.email}`);
            return newToken;
        } catch (error) {
            logger.error('Token刷新失败:', error.message);
            throw error;
        }
    }

    // 更新用户token到数据库
    static async updateUserToken(uuid, token, expiresIn = '7d') {
        try {
            const database = require('../database');

            // 计算过期时间（7天后）
            const expiresAt = new Date();
            expiresAt.setDate(expiresAt.getDate() + 7);

            const sql = `
                UPDATE users
                SET auth_token = ?, token_expires_at = ?, updated_at = NOW()
                WHERE uuid = ?
            `;
            await database.query(sql, [token, expiresAt, uuid]);

            logger.info(`用户token更新成功: ${uuid}`);
        } catch (error) {
            logger.error('更新用户token失败:', error.message);
            throw error;
        }
    }

    // 清除用户token
    static async clearUserToken(uuid) {
        try {
            const database = require('../database');

            const sql = `
                UPDATE users
                SET auth_token = NULL, token_expires_at = NULL, updated_at = NOW()
                WHERE uuid = ?
            `;
            await database.query(sql, [uuid]);

            logger.info(`用户token清除成功: ${uuid}`);
        } catch (error) {
            logger.error('清除用户token失败:', error.message);
            throw error;
        }
    }
}

module.exports = TokenUtils;
