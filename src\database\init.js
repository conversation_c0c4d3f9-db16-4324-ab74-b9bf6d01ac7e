const database = require('../database');
const logger = require('../logger');

// 数据库表结构定义
const tableSchemas = {
    // 用户数据表
    users: `
        CREATE TABLE IF NOT EXISTS \`users\` (
            \`uuid\` VARCHAR(36) NOT NULL PRIMARY KEY COMMENT '用户唯一标识',
            \`email\` VARCHAR(255) NOT NULL UNIQUE COMMENT '用户邮箱',
            \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX \`idx_email\` (\`email\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='用户数据表'
    `,
    
    // Token 存储表
    tokens: `
        CREATE TABLE IF NOT EXISTS \`tokens\` (
            \`id\` INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
            \`token\` TEXT NOT NULL COMMENT 'Augment访问令牌',
            \`tenant_url\` VARCHAR(500) NOT NULL COMMENT '租户URL',
            \`creator_email\` VARCHAR(255) NOT NULL COMMENT '创建者邮箱',
            \`user_ck\` VARCHAR(255) DEFAULT NULL COMMENT '使用者CK',
            \`use_time\` TIMESTAMP NULL DEFAULT NULL COMMENT '使用时间',
            \`destroy_time\` TIMESTAMP NULL DEFAULT NULL COMMENT '销毁时间',
            \`created_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            \`updated_at\` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX \`idx_creator_email\` (\`creator_email\`),
            INDEX \`idx_user_ck\` (\`user_ck\`),
            INDEX \`idx_use_time\` (\`use_time\`),
            INDEX \`idx_destroy_time\` (\`destroy_time\`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='Token存储表'
    `
};

// 初始化数据库表
async function initTables() {
    try {
        logger.info('开始初始化数据库表...');
        
        // 创建用户表
        await createTable('users');
        
        // 创建Token表
        await createTable('tokens');
        
        logger.info('数据库表初始化完成');
        
    } catch (error) {
        logger.error('数据库表初始化失败:', error.message);
        throw error;
    }
}

// 创建单个表
async function createTable(tableName) {
    try {
        const schema = tableSchemas[tableName];
        if (!schema) {
            throw new Error(`未找到表 ${tableName} 的结构定义`);
        }
        
        // 检查表是否已存在
        const exists = await database.checkTable(tableName);
        if (exists) {
            logger.info(`表 ${tableName} 已存在，跳过创建`);
            return;
        }
        
        // 创建表
        await database.query(schema);
        logger.info(`表 ${tableName} 创建成功`);
        
    } catch (error) {
        logger.error(`创建表 ${tableName} 失败:`, error.message);
        throw error;
    }
}

module.exports = {
    initTables,
    createTable,
    tableSchemas
};
