<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - Augment2API</title>

    <!-- 引入导航组件样式 -->
    <link rel="stylesheet" href="/components/nav.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        /* 页面特定样式 */
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            margin-right: 20px;
        }
        
        .profile-info h1 {
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .profile-info p {
            color: #718096;
            font-size: 1.1em;
        }
        
        .profile-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-top: 30px;
        }
        
        .info-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-section h3 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            color: #4a5568;
            font-weight: 500;
        }
        
        .info-value {
            color: #2d3748;
            font-weight: 500;
        }

        /* UUID和Token样式美化 */
        .info-value[id="userUuid"],
        .info-value[id="userToken"] {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;
            flex: 1;
            margin-left: 20px;
        }

        .info-value[id="userUuid"]:hover,
        .info-value[id="userToken"]:hover {
            background: #e6f3ff;
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .info-value[id="userUuid"]:hover::after {
            content: " 📋 点击复制";
            color: #667eea;
            font-size: 12px;
            margin-left: 8px;
        }

        .info-value[id="userToken"]:hover::after {
            content: " 📋 点击复制";
            color: #667eea;
            font-size: 12px;
            margin-left: 8px;
        }

        .token-value {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            cursor: pointer;
            position: relative;
            word-break: break-all;
            line-height: 1.4;
            color: #2d3748;
            font-weight: 500;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .token-value:hover {
            background: #e6f3ff;
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
        }

        .token-value:hover::after {
            content: " 📋 点击复制";
            color: #667eea;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .empty-state, .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state .icon, .loading-state .icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #4a5568;
        }

        .token-item {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .token-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }

        .token-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.15);
        }

        .token-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .token-id {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .token-id::before {
            content: '🔑';
            font-size: 18px;
        }

        .token-status {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .token-status.active {
            background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
            color: #22543d;
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
        }

        .token-status.inactive {
            background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
            color: #c53030;
            box-shadow: 0 2px 8px rgba(197, 48, 48, 0.3);
        }

        .token-content {
            background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            border: 1px solid #e2e8f0;
        }

        .token-value {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: 14px;
            color: #4a5568;
            word-break: break-all;
            line-height: 1.6;
            background: white;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin-right: 60px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }

        .copy-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .token-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        @media (max-width: 768px) {
            .token-meta {
                grid-template-columns: 1fr;
            }
        }

        /* 分页控件样式 */
        .token-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .token-stats {
            color: #4a5568;
            font-weight: 500;
        }

        .pagination-controls select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            font-size: 14px;
            cursor: pointer;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 20px;
            padding: 20px;
        }

        .page-btn {
            padding: 10px 20px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .page-btn:hover:not(:disabled) {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-info {
            color: #4a5568;
            font-weight: 500;
            min-width: 120px;
            text-align: center;
        }

        @media (max-width: 768px) {
            .token-controls {
                flex-direction: column;
                gap: 10px;
            }

            .pagination {
                flex-wrap: wrap;
                gap: 10px;
            }
        }

        .meta-item {
            background: #f8fafc;
            padding: 12px 16px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .meta-label {
            font-size: 12px;
            color: #718096;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .meta-value {
            font-size: 14px;
            color: #2d3748;
            font-weight: 600;
            word-break: break-word;
        }

        .meta-value.url {
            color: #667eea;
            text-decoration: none;
        }

        .meta-value.url:hover {
            text-decoration: underline;
        }
        
        .logout-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #c53030;
            transform: translateY(-1px);
        }
        
        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .profile-header {
                flex-direction: column;
                text-align: center;
            }
            
            .avatar {
                margin-right: 0;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏容器 -->
    <div id="app-nav"></div>

    <div class="container">
        <div class="profile-card">
            <div class="profile-header">
                <div class="avatar" id="userAvatar">👤</div>
                <div class="profile-info">
                    <h1 id="userName">加载中...</h1>
                    <p id="userEmail">加载中...</p>
                </div>
            </div>

            <div class="profile-content">
                <div class="info-section">
                    <h3>📊 账户信息</h3>
                    <div class="info-item">
                        <span class="info-label">用户ID</span>
                        <span class="info-value" id="userUuid" onclick="copyUserUuid()" title="点击复制用户ID">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Token</span>
                        <span class="info-value token-value" id="userToken" onclick="copyUserToken()" title="点击复制Token">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">注册时间</span>
                        <span class="info-value" id="userCreatedAt">-</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">最后更新</span>
                        <span class="info-value" id="userUpdatedAt">-</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>🔑 我的Token</h3>
                    <div class="token-controls">
                        <div class="token-stats">
                            <span id="tokenCount">总计: 0 个Token</span>
                        </div>
                        <div class="pagination-controls">
                            <select id="pageSize">
                                <option value="5">每页 5 条</option>
                                <option value="10" selected>每页 10 条</option>
                                <option value="20">每页 20 条</option>
                                <option value="50">每页 50 条</option>
                            </select>
                        </div>
                    </div>
                    <div id="tokenList">
                        <div class="loading-state">
                            <div class="icon">⏳</div>
                            <p>加载中...</p>
                        </div>
                    </div>
                    <div id="pagination" class="pagination" style="display: none;">
                        <button id="prevPage" class="page-btn">上一页</button>
                        <div id="pageInfo" class="page-info">第 1 页 / 共 1 页</div>
                        <button id="nextPage" class="page-btn">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入导航组件脚本 -->
    <script src="/components/nav.js"></script>

    <script>
        let currentUser = null;

        // 登录状态检查现在由导航组件处理

        // 获取当前JWT token
        function getCurrentJWTToken() {
            // 从Cookie中获取
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'auth_token') {
                    return value;
                }
            }

            // 从localStorage获取
            const localToken = localStorage.getItem('auth_token');
            if (localToken) {
                return localToken;
            }

            return null;
        }

        // 显示用户信息
        function showUserInfo(user) {
            if (!user) return;

            // 设置头像（使用邮箱首字母）
            const avatar = document.getElementById('userAvatar');
            if (user.email) {
                avatar.textContent = user.email.charAt(0).toUpperCase();
            }

            // 设置用户名和邮箱
            document.getElementById('userName').textContent = user.email.split('@')[0];
            document.getElementById('userEmail').textContent = user.email;

            // 设置账户信息
            document.getElementById('userUuid').textContent = user.uuid || '-';

            // 显示JWT token
            const jwtToken = getCurrentJWTToken();
            const tokenElement = document.getElementById('userToken');
            if (jwtToken) {
                tokenElement.textContent = jwtToken;
                tokenElement.title = jwtToken; // 鼠标悬停显示完整token
            } else {
                tokenElement.textContent = '未找到Token';
            }

            if (user.created_at) {
                const createdAt = new Date(user.created_at);
                document.getElementById('userCreatedAt').textContent = createdAt.toLocaleString('zh-CN');
            }

            if (user.updated_at) {
                const updatedAt = new Date(user.updated_at);
                document.getElementById('userUpdatedAt').textContent = updatedAt.toLocaleString('zh-CN');
            }
        }

        // 登出功能
        async function logout() {
            try {
                const response = await fetch('/api/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    // 清除本地存储的token
                    localStorage.removeItem('auth_token');
                    // 跳转到登录页
                    window.location.href = '/login';
                } else {
                    console.error('登出失败');
                }
            } catch (error) {
                console.error('登出错误:', error);
            }
        }

        // 加载用户token
        async function loadUserTokens() {
            try {
                const response = await fetch('/api/user/tokens');
                if (response.ok) {
                    const result = await response.json();
                    showTokenList(result.tokens);
                } else {
                    showTokenError('加载token失败');
                }
            } catch (error) {
                console.error('加载token失败:', error);
                showTokenError('网络错误');
            }
        }

        // 分页相关变量
        let allTokens = [];
        let currentPage = 1;
        let pageSize = 10;

        // 显示token列表
        function showTokenList(tokens) {
            allTokens = tokens || [];
            const tokenCount = document.getElementById('tokenCount');
            const pagination = document.getElementById('pagination');

            // 更新统计信息
            tokenCount.textContent = `总计: ${allTokens.length} 个Token`;

            if (allTokens.length === 0) {
                const tokenList = document.getElementById('tokenList');
                tokenList.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">🔑</div>
                        <h3>暂无Token</h3>
                        <p>您还没有创建任何Token</p>
                    </div>
                `;
                pagination.style.display = 'none';
                return;
            }

            // 显示分页控件
            pagination.style.display = 'flex';
            renderTokenPage();
        }

        // 渲染当前页的Token
        function renderTokenPage() {
            const tokenList = document.getElementById('tokenList');
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageTokens = allTokens.slice(startIndex, endIndex);

            tokenList.innerHTML = pageTokens.map(token => `
                <div class="token-item">
                    <div class="token-header">
                        <span class="token-id">Token #${token.id}</span>
                    </div>
                    <div class="token-content">
                        <div class="token-value">${token.token}</div>
                        <button class="copy-btn" onclick="copyToken('${token.token}')">复制</button>
                    </div>
                    ${token.tenant_url ? `
                        <div class="token-content">
                            <div class="token-value">${token.tenant_url}</div>
                            <button class="copy-btn" onclick="copyTenantUrl('${token.tenant_url}')">复制</button>
                        </div>
                    ` : ''}
                    <div class="token-meta">
                        <div class="meta-item">
                            <div class="meta-label">创建时间</div>
                            <div class="meta-value">${new Date(token.created_at).toLocaleString('zh-CN')}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">使用者</div>
                            <div class="meta-value">${token.user_ck || '-'}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">最后使用</div>
                            <div class="meta-value">${token.use_time ? new Date(token.use_time).toLocaleString('zh-CN') : '未使用'}</div>
                        </div>
                    </div>
                </div>
            `).join('');

            updatePaginationInfo();
        }

        // 更新分页信息
        function updatePaginationInfo() {
            const totalPages = Math.ceil(allTokens.length / pageSize);
            const pageInfo = document.getElementById('pageInfo');
            const prevBtn = document.getElementById('prevPage');
            const nextBtn = document.getElementById('nextPage');

            pageInfo.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
            prevBtn.disabled = currentPage <= 1;
            nextBtn.disabled = currentPage >= totalPages;
        }

        // 显示token加载错误
        function showTokenError(message) {
            const tokenList = document.getElementById('tokenList');
            tokenList.innerHTML = `
                <div class="empty-state">
                    <div class="icon">❌</div>
                    <h3>加载失败</h3>
                    <p>${message}</p>
                </div>
            `;
        }

        // 复制用户UUID到剪贴板
        async function copyUserUuid() {
            const uuid = document.getElementById('userUuid').textContent;
            if (!uuid || uuid === '-') {
                alert('未找到用户ID');
                return;
            }

            try {
                await navigator.clipboard.writeText(uuid);
                // 简单的提示
                const uuidElement = document.getElementById('userUuid');
                const originalText = uuidElement.textContent;
                uuidElement.textContent = '✅ 已复制到剪贴板';
                uuidElement.style.color = '#48bb78';
                setTimeout(() => {
                    uuidElement.textContent = originalText;
                    uuidElement.style.color = '#2d3748';
                }, 2000);
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动复制');
            }
        }

        // 复制JWT token到剪贴板
        async function copyUserToken() {
            const jwtToken = getCurrentJWTToken();
            if (!jwtToken) {
                alert('未找到Token');
                return;
            }

            try {
                await navigator.clipboard.writeText(jwtToken);
                // 简单的提示
                const tokenElement = document.getElementById('userToken');
                const originalText = tokenElement.textContent;
                tokenElement.textContent = '✅ 已复制到剪贴板';
                tokenElement.style.color = '#48bb78';
                setTimeout(() => {
                    tokenElement.textContent = originalText;
                    tokenElement.style.color = '#2d3748';
                }, 2000);
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动复制');
            }
        }

        // 复制Augment token到剪贴板
        async function copyToken(token) {
            try {
                await navigator.clipboard.writeText(token);
                // 简单的提示
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '已复制';
                btn.style.background = '#48bb78';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }, 2000);
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动复制');
            }
        }

        // 复制租户URL到剪贴板
        async function copyTenantUrl(url) {
            try {
                await navigator.clipboard.writeText(url);
                // 简单的提示
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '已复制';
                btn.style.background = '#48bb78';
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }, 2000);
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败，请手动复制');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', async () => {
            // 初始化导航栏
            await AppNav.init({
                showUserInfo: true,
                showProfileBtn: false, // 当前就是个人中心页面，不显示个人中心按钮
                showHomeBtn: true,
                customButtons: [
                    {
                        text: 'API文档',
                        href: '/api-docs',
                        type: 'primary',
                        icon: '📚'
                    },
                    {
                        text: '外部接口测试',
                        href: '/external-test',
                        type: 'primary',
                        icon: '🧪'
                    }
                ]
            });

            // 初始化分页事件监听器
            initPaginationEvents();

            // 获取当前用户信息
            const currentUser = AppNav.getCurrentUser();
            if (currentUser) {
                showUserInfo(currentUser);
                // 加载用户token
                await loadUserTokens();
            }
        });

        // 初始化分页事件
        function initPaginationEvents() {
            // 每页条数选择
            document.getElementById('pageSize').addEventListener('change', (e) => {
                pageSize = parseInt(e.target.value);
                currentPage = 1; // 重置到第一页
                renderTokenPage();
            });

            // 上一页
            document.getElementById('prevPage').addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTokenPage();
                }
            });

            // 下一页
            document.getElementById('nextPage').addEventListener('click', () => {
                const totalPages = Math.ceil(allTokens.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTokenPage();
                }
            });
        }
    </script>
</body>
</html>
