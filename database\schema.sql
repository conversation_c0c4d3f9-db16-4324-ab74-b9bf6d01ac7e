-- Augment2API 数据库表结构
-- 数据库: augmenttoken
-- 字符集: utf8
-- 排序规则: utf8_general_ci

-- 用户数据表
CREATE TABLE IF NOT EXISTS `users` (
    `uuid` VARCHAR(36) NOT NULL PRIMARY KEY COMMENT '用户唯一标识',
    `email` VARCHAR(255) NOT NULL UNIQUE COMMENT '用户邮箱',
    `auth_token` VARCHAR(32) DEFAULT NULL COMMENT '用户认证Token',
    `token_expires_at` TIMESTAMP NULL DEFAULT NULL COMMENT 'Token过期时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_email` (`email`),
    INDEX `idx_auth_token` (`auth_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='用户数据表';

-- Token 存储表
CREATE TABLE IF NOT EXISTS `tokens` (
    `id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增ID',
    `token` TEXT NOT NULL COMMENT 'Augment访问令牌',
    `tenant_url` VARCHAR(500) NOT NULL COMMENT '租户URL',
    `creator_email` VARCHAR(255) NOT NULL COMMENT '创建者邮箱',
    `user_ck` VARCHAR(255) DEFAULT NULL COMMENT '使用者CK',
    `use_time` TIMESTAMP NULL DEFAULT NULL COMMENT '使用时间',
    `destroy_time` TIMESTAMP NULL DEFAULT NULL COMMENT '销毁时间',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_creator_email` (`creator_email`),
    INDEX `idx_user_ck` (`user_ck`),
    INDEX `idx_use_time` (`use_time`),
    INDEX `idx_destroy_time` (`destroy_time`),
    FOREIGN KEY (`creator_email`) REFERENCES `users`(`email`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='Token存储表';
