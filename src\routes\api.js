const express = require('express');
const apiHandler = require('../api');
const { jwtAuthMiddleware } = require('../middleware/auth');

const router = express.Router();

// 先验证用户登录状态，再验证API token
router.use(jwtAuthMiddleware);
router.use(apiHandler.authMiddleware);

// OpenAI兼容的API端点
router.post('/v1/chat/completions', apiHandler.chatCompletions);
router.post('/v1', apiHandler.chatCompletions);
router.post('/v1/chat', apiHandler.chatCompletions);
router.get('/v1/models', apiHandler.getModels);

module.exports = router;
