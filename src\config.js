const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const logger = require('./logger');

class Config {
    constructor() {
        this.config = {};
        this.version = 'v1.0.9';
        this.configFile = path.join(process.cwd(), 'config.yaml');
    }

    init() {
        try {
            // 从YAML文件读取配置
            if (fs.existsSync(this.configFile)) {
                const yamlContent = fs.readFileSync(this.configFile, 'utf8');
                this.config = yaml.load(yamlContent);
                logger.info('从 config.yaml 加载配置成功');
            } else {
                logger.error('config.yaml 文件不存在');
                throw new Error('配置文件不存在');
            }

            logger.info(`Welcome to use Augment2Api! Current Version: ${this.version}`);
            logger.info('Augment2Api配置加载完成');

        } catch (error) {
            logger.error('配置加载失败:', error.message);
            throw error;
        }
    }


    
    // 支持嵌套路径访问，如 'database.mysql.host'
    get(path) {
        if (!path) return this.config;

        const keys = path.split('.');
        let result = this.config;

        for (const key of keys) {
            if (result && typeof result === 'object' && key in result) {
                result = result[key];
            } else {
                return undefined;
            }
        }

        return result;
    }

    // 支持嵌套路径设置
    set(path, value) {
        const keys = path.split('.');
        let current = this.config;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            current = current[key];
        }

        current[keys[keys.length - 1]] = value;
    }

    // 获取数据库配置
    getDatabase() {
        return this.get('database.mysql');
    }

    // 获取API配置
    getApi() {
        return this.get('api');
    }

    // 获取应用配置
    getApp() {
        return this.get('app');
    }

    // 获取JWT配置
    getJWT() {
        return this.get('jwt');
    }

    // 获取OAuth配置
    getOAuth() {
        return this.get('oauth');
    }

    getAll() {
        return { ...this.config };
    }
}

module.exports = new Config();
